import { NavLink } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import logoImg from "../../../public/img/dream-logo.png";
import {
  LayoutDashboard,
  FolderTree,
  Component as Components,
  FileText,
  Layout,
  Globe,
  Users,
  LogOut,
  Settings,
} from "lucide-react";
import { Button, Image } from "antd";

const Sidebar = () => {
  const { user, logout } = useAuth();

  const navigationItems = [
    {
      name: "Dashboard",
      href: "/",
      icon: LayoutDashboard,
      adminOnly: false,
    },
    {
      name: "Categories",
      href: "/categories",
      icon: FolderTree,
      adminOnly: true,
    },
    {
      name: "Components",
      href: "/components",
      icon: Components,
      adminOnly: false,
    },
    {
      name: "Pages",
      href: "/pages",
      icon: FileText,
      adminOnly: false,
    },
    {
      name: "Templates",
      href: "/templates",
      icon: Layout,
      adminOnly: false,
    },
    {
      name: "Websites",
      href: "/websites",
      icon: Globe,
      adminOnly: false,
    },
    {
      name: "Users",
      href: "/users",
      icon: Users,
      adminOnly: true,
    },
  ];

  const filteredItems = navigationItems.filter(
    (item) => !item.adminOnly || user?.role === "admin"
  );

  return (
    <div className="tw-bg-white tw-shadow-lg tw-h-screen tw-w-64 tw-fixed tw-left-0 tw-top-0 tw-z-40">
      <div className="tw-p-[25px] tw-border-b tw-border-gray-200">
        <div className="tw-flex tw-items-center">
          <Image
            preview={false}
            src={logoImg}
            alt="Logo"
            className="tw-w-auto tw-h-auto tw-aspect-auto tw-object-contain tw-mx-auto"
          />
        </div>
      </div>

      <nav className="tw-mt-6 tw-px-4">
        <div className="tw-space-y-2">
          {filteredItems.map((item) => (
            <NavLink
              key={item.name}
              to={item.href}
              className={({ isActive }) =>
                `tw-flex tw-items-center tw-px-4 tw-py-3 tw-text-sm tw-font-medium tw-rounded-lg tw-transition-all tw-duration-200 ${
                  isActive
                    ? "tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-shadow-lg"
                    : "tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-hover:tw-text-gray-900"
                }`
              }
            >
              <item.icon className="tw-w-5 tw-h-5 tw-mr-3" />
              {item.name}
            </NavLink>
          ))}
        </div>
      </nav>

      <div className="tw-absolute tw-bottom-0 tw-w-full tw-p-4 tw-border-t tw-border-gray-200 ">
        <button
          type="text"
          onClick={logout}
          className="!tw-text-[#FB3748] tw-flex tw-items-center tw-w-full tw-px-3 tw-py-2 tw-text-sm tw-hover:tw-bg-gray-200 tw-rounded-lg tw-transition-colors"
        >
          <LogOut color="#FB3748" className="tw-w-4 tw-h-4 tw-mr-2" />
          Log Out
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
