import React, { useState, useEffect } from "react";
import Sidebar from "../Layout/Sidebar";
import Header from "../Layout/Header";
import TemplateEditor from "./TemplateEditor";
import { Plus, Layout, Edit2, Trash2, Eye, Search, Copy } from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";

const TemplateManager = () => {
  const [templates, setTemplates] = useState([]);
  const [pages, setPages] = useState([]);
  const [showEditor, setShowEditor] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const api = useHttp();

  useEffect(() => {
    // Fetch templates
    api.sendRequest(CONSTANTS.API.templates.get, (res) => {
      console.log("Templates fetched:", res);
      setTemplates(res);
    });

    // Fetch pages
    api.sendRequest(CONSTANTS.API.pages.get, (res) => {
      console.log("Pages fetched:", res);
      setPages(res);
    });
  }, []);

  const refreshData = () => {
    // Fetch templates
    api.sendRequest(CONSTANTS.API.templates.get, (res) => {
      setTemplates(res);
    });

    // Fetch pages
    api.sendRequest(CONSTANTS.API.pages.get, (res) => {
      setPages(res);
    });
  };

  const handleEdit = (template) => {
    setEditingTemplate(template);
    setShowEditor(true);
  };

  const handleDelete = async (id) => {
    if (confirm("Are you sure you want to delete this template?")) {
      api.sendRequest(
        apiGenerator(CONSTANTS.API.templates.delete, { id }),
        (res) => {
          console.log("Template deleted successfully:", res);
          refreshData();
        },
        null,
        "Template deleted successfully!"
      );
    }
  };

  const handleDuplicate = async (template) => {
    const duplicateData = {
      name: `${template.name} (Copy)`,
      description: template.description,
      pages: template.pages,
    };

    api.sendRequest(
      CONSTANTS.API.templates.create,
      (res) => {
        console.log("Template duplicated successfully:", res);
        refreshData();
      },
      duplicateData,
      "Template duplicated successfully!"
    );
  };

  const handleSave = async () => {
    refreshData();
    setShowEditor(false);
    setEditingTemplate(null);
  };

  const filteredTemplates = templates.filter(
    (template) =>
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (template.description &&
        template.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (api.loading) {
    return (
      <div className="tw-flex">
        <Sidebar />
        <div className="tw-flex-1 tw-ml-64">
          <div className="tw-animate-pulse tw-p-6">
            <div className="tw-h-8 tw-bg-gray-300 tw-rounded tw-mb-4"></div>
            <div className="tw-grid tw-grid-cols-1 tw-md:tw-grid-cols-2 tw-lg:tw-grid-cols-3 tw-gap-6">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div
                  key={i}
                  className="tw-h-64 tw-bg-gray-300 tw-rounded"
                ></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (showEditor) {
    return (
      <div className="tw-flex">
        <Sidebar />
        <div className="tw-flex-1 tw-ml-64">
          <TemplateEditor
            template={editingTemplate}
            pages={pages}
            onSave={handleSave}
            onCancel={() => {
              setShowEditor(false);
              setEditingTemplate(null);
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="tw-flex">
      <Sidebar />
      <div className="tw-flex-1 tw-ml-64">
        <Header
          title="Template Manager"
          subtitle="Create and manage page templates for your websites"
        />

        <div className="tw-p-6">
          <div className="tw-flex tw-flex-col tw-lg:tw-flex-row tw-justify-between tw-items-start tw-lg:tw-items-center tw-mb-6 tw-space-y-4 tw-lg:tw-space-y-0">
            <div className="tw-flex tw-items-center">
              <Layout className="tw-w-6 tw-h-6 tw-text-blue-600 tw-mr-2" />
              <h2 className="tw-text-xl tw-font-semibold tw-text-gray-900">
                Templates ({filteredTemplates.length})
              </h2>
            </div>

            <div className="tw-flex tw-flex-col tw-sm:tw-flex-row tw-space-y-2 tw-sm:tw-space-y-0 tw-sm:tw-space-x-4 tw-w-full tw-lg:tw-w-auto">
              {/* Search */}
              <div className="tw-relative">
                <Search className="tw-absolute tw-left-3 tw-top-1/2 tw-transform tw--translate-y-1/2 tw-w-4 tw-h-4 tw-text-gray-400" />
                <input
                  type="text"
                  placeholder="Search templates..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="tw-pl-10 tw-pr-4 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-w-full tw-sm:tw-w-64"
                />
              </div>

              <button
                onClick={() => setShowEditor(true)}
                className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-4 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all tw-flex tw-items-center tw-justify-center tw-whitespace-nowrap"
              >
                <Plus className="tw-w-4 tw-h-4 tw-mr-2" />
                Create Template
              </button>
            </div>
          </div>

          {/* Templates Grid */}
          <div className="tw-grid tw-grid-cols-1 tw-md:tw-grid-cols-2 tw-lg:tw-grid-cols-3 tw-gap-6">
            {filteredTemplates.map((template) => (
              <div
                key={template.id}
                className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-hover:tw-shadow-md tw-transition-all tw-duration-200"
              >
                <div className="tw-p-6">
                  <div className="tw-flex tw-items-center tw-justify-between tw-mb-4">
                    <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-truncate">
                      {template.name}
                    </h3>

                    <div className="tw-flex tw-space-x-2">
                      <button
                        onClick={() => handleDuplicate(template)}
                        className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-green-600 tw-hover:tw-bg-green-50 tw-rounded-lg tw-transition-colors"
                        title="Duplicate Template"
                      >
                        <Copy className="tw-w-4 tw-h-4" />
                      </button>
                      <button
                        onClick={() => handleEdit(template)}
                        className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-blue-600 tw-hover:tw-bg-blue-50 tw-rounded-lg tw-transition-colors"
                      >
                        <Edit2 className="tw-w-4 tw-h-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(template.id)}
                        className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-red-600 tw-hover:tw-bg-red-50 tw-rounded-lg tw-transition-colors"
                      >
                        <Trash2 className="tw-w-4 tw-h-4" />
                      </button>
                    </div>
                  </div>

                  <div className="tw-mb-4">
                    <p className="tw-text-sm tw-text-gray-600 tw-mb-3">
                      {template.description || "No description provided"}
                    </p>

                    <div className="tw-mb-3">
                      <p className="tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                        Pages: {template.pages ? template.pages.length : 0}
                      </p>

                      {template.pages && template.pages.length > 0 && (
                        <div className="tw-bg-gray-50 tw-rounded-lg tw-p-3">
                          <div className="tw-text-xs tw-text-gray-600">
                            {template.pages.slice(0, 3).map((pageId, index) => {
                              const page = pages.find((p) => p.id === pageId);
                              return (
                                <div
                                  key={index}
                                  className="tw-flex tw-items-center tw-mb-1"
                                >
                                  <div className="tw-w-2 tw-h-2 tw-bg-blue-500 tw-rounded-full tw-mr-2"></div>
                                  {page ? page.name : `Page #${pageId}`}
                                </div>
                              );
                            })}
                            {template.pages.length > 3 && (
                              <div className="tw-text-gray-500 tw-mt-1">
                                +{template.pages.length - 3} more pages
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="tw-flex tw-justify-between tw-items-center tw-text-xs tw-text-gray-500">
                    <span>v{template.version}</span>
                    <span>
                      {new Date(template.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredTemplates.length === 0 && (
            <div className="tw-text-center tw-py-12">
              <Layout className="tw-w-16 tw-h-16 tw-text-gray-300 tw-mx-auto tw-mb-4" />
              <h3 className="tw-text-lg tw-font-medium tw-text-gray-900 tw-mb-2">
                {searchTerm ? "No templates found" : "No templates yet"}
              </h3>
              <p className="tw-text-gray-500 tw-mb-4">
                {searchTerm
                  ? "Try adjusting your search criteria"
                  : "Create your first template by grouping pages together"}
              </p>
              {!searchTerm && (
                <button
                  onClick={() => setShowEditor(true)}
                  className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-6 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all"
                >
                  Create Your First Template
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TemplateManager;
