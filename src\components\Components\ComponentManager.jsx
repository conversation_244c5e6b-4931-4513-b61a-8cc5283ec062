import React, { useState, useEffect } from "react";
import ComponentEditor from "./ComponentEditor";
import {
  Plus,
  Component as Components,
  Edit2,
  Trash2,
  Eye,
  Search,
  Filter,
} from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import { useAuth } from "../../contexts/AuthContext";
import { Select, Tag } from "antd";

const ComponentManager = () => {
  const { user } = useAuth();
  const [components, setComponents] = useState([]);
  const [categories, setCategories] = useState([]);
  const [showEditor, setShowEditor] = useState(false);
  const [editingComponent, setEditingComponent] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const api = useHttp();

  useEffect(() => {
    // Fetch components
    api.sendRequest(CONSTANTS.API.components.get, (res) => {
      console.log("Components fetched:", res);
      setComponents(res);
    });

    // Fetch categories
    api.sendRequest(CONSTANTS.API.categories.get, (res) => {
      console.log("Categories fetched:", res);
      setCategories(res);
    });
  }, []);

  const refreshData = () => {
    // Fetch components
    api.sendRequest(CONSTANTS.API.components.get, (res) => {
      setComponents(res);
    });

    // Fetch categories
    api.sendRequest(CONSTANTS.API.categories.get, (res) => {
      setCategories(res);
    });
  };

  const handleEdit = (component) => {
    setEditingComponent(component);
    setShowEditor(true);
  };

  const handleDelete = async (id) => {
    if (confirm("Are you sure you want to delete this component?")) {
      api.sendRequest(
        apiGenerator(CONSTANTS.API.components.delete, { id }),
        (res) => {
          console.log("Component deleted successfully:", res);
          refreshData();
        },
        null,
        "Component deleted successfully!"
      );
    }
  };

  const handleSave = async () => {
    refreshData();
    setShowEditor(false);
    setEditingComponent(null);
  };

  const filteredComponents = components.filter((component) => {
    const matchesSearch =
      component.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (component.category_name &&
        component.category_name
          .toLowerCase()
          .includes(searchTerm.toLowerCase()));
    const matchesCategory =
      !selectedCategory || component.category_id === parseInt(selectedCategory);
    return matchesSearch && matchesCategory;
  });

  if (api.isLoading) {
    return (
      <div className="tw-flex">
        <Sidebar />
        <div className="tw-flex-1 tw-ml-64">
          <div className="tw-animate-pulse tw-p-6">
            <div className="tw-h-8 tw-bg-gray-300 tw-rounded tw-mb-4"></div>
            <div className="tw-grid tw-grid-cols-1 tw-md:tw-grid-cols-2 tw-lg:tw-grid-cols-3 tw-gap-6">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div
                  key={i}
                  className="tw-h-64 tw-bg-gray-300 tw-rounded"
                ></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (showEditor) {
    return (
      <ComponentEditor
        component={editingComponent}
        categories={categories}
        onSave={handleSave}
        onCancel={() => {
          setShowEditor(false);
          setEditingComponent(null);
        }}
      />
    );
  }

  return (
    <div className="tw-p-6">
      <div className="tw-flex tw-flex-col tw-lg:tw-flex-row tw-justify-between tw-items-start tw-lg:tw-items-center tw-mb-6 tw-space-y-4 tw-lg:tw-space-y-0">
        <div className="tw-flex tw-items-center tw-justify-between tw-w-full">
          {/* <div className="tw-flex tw-items-center"> */}
          {/* <Components className="tw-w-6 tw-h-6 tw-text-blue-600 tw-mr-2" /> */}
          <h2 className="tw-text-xl tw-font-bold tw-text-gray-900">
            Components ({filteredComponents.length})
          </h2>
          {/* </div> */}
          {user?.role === "admin" && (
            <button
              onClick={() => setShowEditor(true)}
              className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-4 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all tw-flex tw-items-center tw-justify-center tw-whitespace-nowrap"
            >
              <Plus className="tw-w-4 tw-h-4 tw-mr-2" />
              Add Component
            </button>
          )}
        </div>

        {filteredComponents?.length ? (
          <div className="tw-flex tw-items-center tw-justify-between tw-w-full tw-space-x-4">
            <div className="tw-w-full">
              <Search className="tw-absolute tw-left-3 tw-top-1/2 tw-transform tw--translate-y-1/2 tw-w-4 tw-h-4 tw-text-gray-400" />
              <input
                type="text"
                placeholder="Search components..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="tw-pl-10 tw-pr-4 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-w-full tw-sm:tw-w-64"
              />
            </div>
            <div className="tw-flex tw-items-center">
              {/* <Filter className="tw-mr-2 tw-w-4 tw-h-4 tw-text-gray-400" /> */}
              <Select
                prefix={
                  <Filter
                    width={16}
                    height={16}
                    className=" tw-text-gray-400"
                  />
                }
                value={selectedCategory}
                onChange={setSelectedCategory}
                style={{ width: 200, height: 42 }}
                placeholder="Select Category"
              >
                <Select.Option value="">All Categories</Select.Option>
                {categories?.map((category) => (
                  <Select.Option key={category.id} value={category.id}>
                    {category.name}
                  </Select.Option>
                ))}
              </Select>
            </div>
            {/* <div className="">
                  <Filter className="tw-left-3 tw-top-1/2 tw-transform tw--translate-y-1/2 tw-w-4 tw-h-4 tw-text-gray-400" />
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="tw-pl-10 tw-pr-8 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-appearance-none tw-bg-white"
                  >
                    <option value="">All Categories</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div> */}
          </div>
        ) : (
          <></>
        )}

        {/* <div className="tw-flex tw-flex-col tw-sm:tw-flex-row tw-space-y-2 tw-sm:tw-space-y-0 tw-sm:tw-space-x-4 tw-w-full tw-lg:tw-w-auto">
              <div className="tw-relative">
                <Search className="tw-absolute tw-left-3 tw-top-1/2 tw-transform tw--translate-y-1/2 tw-w-4 tw-h-4 tw-text-gray-400" />
                <input
                  type="text"
                  placeholder="Search components..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="tw-pl-10 tw-pr-4 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-w-full tw-sm:tw-w-64"
                />
              </div>

              <div className="tw-relative">
                <Filter className="tw-absolute tw-left-3 tw-top-1/2 tw-transform tw--translate-y-1/2 tw-w-4 tw-h-4 tw-text-gray-400" />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="tw-pl-10 tw-pr-8 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-appearance-none tw-bg-white"
                >
                  <option value="">All Categories</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
            </div> */}
      </div>

      {/* Components Grid */}
      <div className="tw-grid tw-grid-cols-1 tw-md:tw-grid-cols-2 tw-lg:tw-grid-cols-3 tw-gap-6">
        {filteredComponents.map((component) => (
          <div
            key={component.id}
            className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-hover:tw-shadow-md tw-transition-all tw-duration-200"
          >
            <div className="tw-p-4 tw-border-b tw-border-gray-100">
              <div className="tw-flex tw-items-center tw-justify-between tw-mb-2">
                <div>
                  <Tag
                    bordered={false}
                    color="purple"
                    className="tw-rounded-xl tw-py-[2px]"
                  >
                    {component?.category_name}
                  </Tag>
                  <Tag color="default" className="tw-rounded-xl">
                    v{component.version}
                  </Tag>
                </div>

                {user?.role === "admin" && (
                  <div className="tw-flex tw-space-x-2">
                    <button
                      onClick={() => handleEdit(component)}
                      className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-blue-600 tw-hover:tw-bg-blue-50 tw-rounded-lg tw-transition-colors"
                    >
                      <Edit2 className="tw-w-4 tw-h-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(component.id)}
                      className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-red-600 tw-hover:tw-bg-red-50 tw-rounded-lg tw-transition-colors"
                    >
                      <Trash2 className="tw-w-4 tw-h-4 tw-text-red-600" />
                    </button>
                  </div>
                )}
              </div>

              <div className="tw-flex tw-items-center tw-space-x-2 tw-justify-between">
                {/* {component.category_color && (
                      <div
                        className="tw-w-3 tw-h-3 tw-rounded-full"
                        style={{ backgroundColor: component.category_color }}
                      />
                    )} */}
                <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-truncate">
                  {component.name}
                </h3>
                <span className="tw-text-gray-500 tw-text-sm">
                  {new Date(component.created_at).toLocaleDateString()}
                </span>
                {/* <span className="tw-text-sm tw-text-gray-500">
                      {component.category_name || "Uncategorized"}
                    </span> */}
              </div>
            </div>

            <div className="tw-p-4">
              <div className="tw-bg-gray-50 tw-rounded-lg tw-p-3 tw-mb-4 tw-max-h-32 tw-overflow-hidden">
                <div
                  className="tw-text-xs tw-text-gray-600 tw-font-mono tw-leading-relaxed"
                  dangerouslySetInnerHTML={{
                    __html: component.html_content.substring(0, 200) + "...",
                  }}
                />
              </div>

              {component.placeholders && component.placeholders.length > 0 && (
                <div className="tw-mb-4">
                  <p className="tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                    Placeholders:
                  </p>
                  <div className="tw-flex tw-flex-wrap tw-gap-1">
                    {component.placeholders
                      .slice(0, 3)
                      .map((placeholder, index) => (
                        <span
                          key={index}
                          className="tw-px-2 tw-py-1 tw-bg-blue-100 tw-text-blue-800 tw-text-xs tw-rounded-full"
                        >
                          ${placeholder}
                        </span>
                      ))}
                    {component.placeholders.length > 3 && (
                      <span className="tw-px-2 tw-py-1 tw-bg-gray-100 tw-text-gray-600 tw-text-xs tw-rounded-full">
                        +{component.placeholders.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* <div className="tw-flex tw-justify-between tw-items-center tw-text-xs tw-text-gray-500">
                    <span>v{component.version}</span>
                    <span>
                      {new Date(component.created_at).toLocaleDateString()}
                    </span>
                  </div> */}
            </div>
          </div>
        ))}
      </div>

      {filteredComponents.length === 0 && (
        <div className="tw-text-center tw-pt-30 tw-h-full">
          <Components className="tw-w-16 tw-h-16 tw-text-gray-400 tw-mx-auto tw-mb-4" />
          <h3 className="tw-text-lg tw-font-medium tw-text-gray-900 tw-mb-2">
            {searchTerm || selectedCategory
              ? "No components found"
              : "No components yet"}
          </h3>
          <p className="tw-text-gray-500 tw-mb-4">
            {searchTerm || selectedCategory
              ? "Try adjusting your search or filter criteria"
              : "Create your first reusable component to get started"}
          </p>
          {user?.role === "admin" && !searchTerm && !selectedCategory && (
            <button
              onClick={() => setShowEditor(true)}
              className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-6 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all"
            >
              Create Component
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default ComponentManager;
