import React, { useState, useEffect } from "react";
import Sidebar from "../Layout/Sidebar";
import Header from "../Layout/Header";
import WebsiteEditor from "./WebsiteEditor";
import {
  Plus,
  Globe,
  Edit2,
  Trash2,
  Download,
  Search,
  Eye,
  ExternalLink,
} from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";

const WebsiteManager = () => {
  const [websites, setWebsites] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [showEditor, setShowEditor] = useState(false);
  const [editingWebsite, setEditingWebsite] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [exporting, setExporting] = useState({});
  const api = useHttp();

  useEffect(() => {
    // Fetch websites
    api.sendRequest(CONSTANTS.API.websites.get, (res) => {
      console.log("Websites fetched:", res);
      setWebsites(res);
    });

    // Fetch templates
    api.sendRequest(CONSTANTS.API.templates.get, (res) => {
      console.log("Templates fetched:", res);
      setTemplates(res);
    });
  }, []);

  const refreshData = () => {
    // Fetch websites
    api.sendRequest(CONSTANTS.API.websites.get, (res) => {
      setWebsites(res);
    });

    // Fetch templates
    api.sendRequest(CONSTANTS.API.templates.get, (res) => {
      setTemplates(res);
    });
  };

  const handleEdit = (website) => {
    setEditingWebsite(website);
    setShowEditor(true);
  };

  const handleDelete = async (id) => {
    if (confirm("Are you sure you want to delete this website?")) {
      api.sendRequest(
        apiGenerator(CONSTANTS.API.websites.delete, { id }),
        (res) => {
          console.log("Website deleted successfully:", res);
          refreshData();
        },
        null,
        "Website deleted successfully!"
      );
    }
  };

  const handleExport = async (websiteId) => {
    setExporting({ ...exporting, [websiteId]: true });

    api.sendRequest(
      apiGenerator(CONSTANTS.API.websites.export, { id: websiteId }),
      (data) => {
        console.log("Website export successful:", data);

        // Download the ZIP file
        const downloadUrl = `${window.location.origin}${data.downloadUrl}`;
        const link = document.createElement("a");
        link.href = downloadUrl;
        link.download = downloadUrl.split("/").pop();
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setExporting({ ...exporting, [websiteId]: false });
      },
      null,
      "Website exported successfully!",
      (error) => {
        console.error("Export failed:", error);
        setExporting({ ...exporting, [websiteId]: false });
      }
    );
  };

  const handleSave = async () => {
    refreshData();
    setShowEditor(false);
    setEditingWebsite(null);
  };

  const filteredWebsites = websites.filter(
    (website) =>
      website.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (website.template_name &&
        website.template_name.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const getStatusColor = (status) => {
    switch (status) {
      case "published":
        return "tw-bg-green-100 tw-text-green-800";
      case "draft":
        return "tw-bg-yellow-100 tw-text-yellow-800";
      default:
        return "tw-bg-gray-100 tw-text-gray-800";
    }
  };

  if (api.loading) {
    return (
      <div className="tw-flex">
        <Sidebar />
        <div className="tw-flex-1 tw-ml-64">
          <div className="tw-animate-pulse tw-p-6">
            <div className="tw-h-8 tw-bg-gray-300 tw-rounded tw-mb-4"></div>
            <div className="tw-grid tw-grid-cols-1 tw-md:tw-grid-cols-2 tw-lg:tw-grid-cols-3 tw-gap-6">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div
                  key={i}
                  className="tw-h-80 tw-bg-gray-300 tw-rounded"
                ></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (showEditor) {
    return (
      <div className="tw-flex">
        <Sidebar />
        <div className="tw-flex-1 tw-ml-64">
          <WebsiteEditor
            website={editingWebsite}
            templates={templates}
            onSave={handleSave}
            onCancel={() => {
              setShowEditor(false);
              setEditingWebsite(null);
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="tw-flex">
      <Sidebar />
      <div className="tw-flex-1 tw-ml-64">
        <Header
          title="Website Manager"
          subtitle="Create, manage, and deploy your websites"
        />

        <div className="tw-p-6">
          <div className="tw-flex tw-flex-col tw-lg:tw-flex-row tw-justify-between tw-items-start tw-lg:tw-items-center tw-mb-6 tw-space-y-4 tw-lg:tw-space-y-0">
            <div className="tw-flex tw-items-center">
              <Globe className="tw-w-6 tw-h-6 tw-text-blue-600 tw-mr-2" />
              <h2 className="tw-text-xl tw-font-semibold tw-text-gray-900">
                Websites ({filteredWebsites.length})
              </h2>
            </div>

            <div className="tw-flex tw-flex-col tw-sm:tw-flex-row tw-space-y-2 tw-sm:tw-space-y-0 tw-sm:tw-space-x-4 tw-w-full tw-lg:tw-w-auto">
              {/* Search */}
              <div className="tw-relative">
                <Search className="tw-absolute tw-left-3 tw-top-1/2 tw-transform tw--translate-y-1/2 tw-w-4 tw-h-4 tw-text-gray-400" />
                <input
                  type="text"
                  placeholder="Search websites..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="tw-pl-10 tw-pr-4 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-w-full tw-sm:tw-w-64"
                />
              </div>

              <button
                onClick={() => setShowEditor(true)}
                className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-4 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all tw-flex tw-items-center tw-justify-center tw-whitespace-nowrap"
              >
                <Plus className="tw-w-4 tw-h-4 tw-mr-2" />
                Create Website
              </button>
            </div>
          </div>

          {/* Websites Grid */}
          <div className="tw-grid tw-grid-cols-1 tw-md:tw-grid-cols-2 tw-lg:tw-grid-cols-3 tw-gap-6">
            {filteredWebsites.map((website) => (
              <div
                key={website.id}
                className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-hover:tw-shadow-md tw-transition-all tw-duration-200"
              >
                {/* Website Preview/Header */}
                <div className="tw-h-32 tw-bg-gradient-to-r tw-from-blue-500 tw-to-purple-600 tw-rounded-t-xl tw-relative tw-overflow-hidden">
                  <div className="tw-absolute tw-inset-0 tw-bg-black tw-bg-opacity-20"></div>
                  <div className="tw-absolute tw-top-4 tw-left-4 tw-text-white">
                    <h4 className="tw-font-semibold tw-text-lg">
                      {website.name}
                    </h4>
                    <p className="tw-text-sm tw-opacity-90">
                      {website.template_name || "No template"}
                    </p>
                  </div>
                  <div className="tw-absolute tw-top-4 tw-right-4">
                    <span
                      className={`tw-px-2 tw-py-1 tw-rounded-full tw-text-xs tw-font-medium ${getStatusColor(
                        website.status
                      )}`}
                    >
                      {website.status || "draft"}
                    </span>
                  </div>
                </div>

                <div className="tw-p-6">
                  {/* Website Info */}
                  <div className="tw-mb-4">
                    <div className="tw-flex tw-items-center tw-mb-2">
                      <div
                        className="tw-w-4 tw-h-4 tw-rounded-full tw-mr-2"
                        style={{ backgroundColor: website.primary_color }}
                      />
                      <span className="tw-text-sm tw-text-gray-600">
                        Primary: {website.primary_color}
                      </span>
                    </div>
                    <div className="tw-flex tw-items-center">
                      <div
                        className="tw-w-4 tw-h-4 tw-rounded-full tw-mr-2"
                        style={{ backgroundColor: website.secondary_color }}
                      />
                      <span className="tw-text-sm tw-text-gray-600">
                        Secondary: {website.secondary_color}
                      </span>
                    </div>
                  </div>

                  {/* Content Summary */}
                  <div className="tw-mb-4">
                    <p className="tw-text-sm tw-text-gray-600">
                      <strong>Content Fields:</strong>{" "}
                      {website.content_data
                        ? Object.keys(website.content_data).length
                        : 0}
                    </p>
                  </div>

                  {/* Action Buttons */}
                  <div className="tw-flex tw-items-center tw-justify-between tw-space-x-2">
                    <div className="tw-flex tw-space-x-2">
                      <button
                        onClick={() => handleEdit(website)}
                        className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-blue-600 tw-hover:tw-bg-blue-50 tw-rounded-lg tw-transition-colors"
                        title="Edit Website"
                      >
                        <Edit2 className="tw-w-4 tw-h-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(website.id)}
                        className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-red-600 tw-hover:tw-bg-red-50 tw-rounded-lg tw-transition-colors"
                        title="Delete Website"
                      >
                        <Trash2 className="tw-w-4 tw-h-4" />
                      </button>
                    </div>

                    <button
                      onClick={() => handleExport(website.id)}
                      disabled={exporting[website.id]}
                      className="tw-bg-green-600 tw-text-white tw-px-3 tw-py-2 tw-rounded-lg tw-text-sm tw-font-medium tw-hover:tw-bg-green-700 tw-transition-colors tw-flex tw-items-center tw-disabled:tw-opacity-50"
                      title="Export Website"
                    >
                      {exporting[website.id] ? (
                        <>
                          <div className="tw-animate-spin tw-rounded-full tw-h-3 tw-w-3 tw-border-b-2 tw-border-white tw-mr-2"></div>
                          Exporting...
                        </>
                      ) : (
                        <>
                          <Download className="tw-w-4 tw-h-4 tw-mr-1" />
                          Export
                        </>
                      )}
                    </button>
                  </div>

                  {/* Metadata */}
                  <div className="tw-flex tw-justify-between tw-items-center tw-text-xs tw-text-gray-500 tw-mt-4 tw-pt-4 tw-border-t tw-border-gray-100">
                    <span>v{website.version}</span>
                    <span>
                      {new Date(website.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredWebsites.length === 0 && (
            <div className="tw-text-center tw-py-12">
              <Globe className="tw-w-16 tw-h-16 tw-text-gray-300 tw-mx-auto tw-mb-4" />
              <h3 className="tw-text-lg tw-font-medium tw-text-gray-900 tw-mb-2">
                {searchTerm ? "No websites found" : "No websites yet"}
              </h3>
              <p className="tw-text-gray-500 tw-mb-4">
                {searchTerm
                  ? "Try adjusting your search criteria"
                  : "Create your first website from a template"}
              </p>
              {!searchTerm && (
                <button
                  onClick={() => setShowEditor(true)}
                  className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-6 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all"
                >
                  Create Your First Website
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WebsiteManager;
