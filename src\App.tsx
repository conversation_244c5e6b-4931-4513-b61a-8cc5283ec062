import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Login from './components/Auth/Login';
import Dashboard from './components/Dashboard/Dashboard';
import CategoryManager from './components/Categories/CategoryManager';
import ComponentManager from './components/Components/ComponentManager';
import PageBuilder from './components/Pages/PageBuilder';
import TemplateManager from './components/Templates/TemplateManager';
import WebsiteManager from './components/Websites/WebsiteManager';
import UserManager from './components/Users/<USER>';
import { AuthProvider, useAuth } from './contexts/AuthContext';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="tw-min-h-screen tw-bg-gray-50">
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/" element={<PrivateRoute><Dashboard /></PrivateRoute>} />
            <Route path="/categories" element={<PrivateRoute><CategoryManager /></PrivateRoute>} />
            <Route path="/components" element={<PrivateRoute><ComponentManager /></PrivateRoute>} />
            <Route path="/pages" element={<PrivateRoute><PageBuilder /></PrivateRoute>} />
            <Route path="/templates" element={<PrivateRoute><TemplateManager /></PrivateRoute>} />
            <Route path="/websites" element={<PrivateRoute><WebsiteManager /></PrivateRoute>} />
            <Route path="/users" element={<AdminRoute><UserManager /></AdminRoute>} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

function PrivateRoute({ children }) {
  const { user, loading } = useAuth();
  
  if (loading) {
    return (
      <div className="tw-min-h-screen tw-flex tw-items-center tw-justify-center">
        <div className="tw-animate-spin tw-rounded-full tw-h-32 tw-w-32 tw-border-b-2 tw-border-blue-600"></div>
      </div>
    );
  }
  
  return user ? children : <Navigate to="/login" />;
}

function AdminRoute({ children }) {
  const { user } = useAuth();
  return (
    user && user.role === 'admin' ? children : <Navigate to="/" />
  );
}

export default App;
