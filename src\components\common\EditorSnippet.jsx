import React, { useState } from "react";
import Editor from "react-simple-code-editor";
import Prism from "prismjs";
import "prismjs/components/prism-markup";
import "prismjs/components/prism-css";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism-dark.css";

const highlightHTML = (code) => {
  return (
    Prism.highlight(code, Prism.languages.markup, "markup")
      // Add custom highlighting for placeholders ${...}
      .replace(
        /(\$\{)([^}]+)(\})/g,
        '<span style="color: #dcdcaa; font-weight: bold;">$1</span><span style="color: #9cdcfe; font-weight: bold;">$2</span><span style="color: #dcdcaa; font-weight: bold;">$3</span>'
      )
  );
};

const highlightCSS = (code) => {
  return Prism.highlight(code, Prism.languages.css, "css");
};

const highlightJS = (code) => {
  return Prism.highlight(code, Prism.languages.javascript, "javascript");
};

const highlight = (code, type) => {
  if (!code) return "";
  switch (type) {
    case "html":
      return highlightHTML(code);
    case "css":
      return highlightCSS(code);
    case "js":
      return highlightJS(code);
    default:
      return highlightHTML(code);
  }
  //   return Prism.highlight(code, Prism.languages.javascript, "javascript");
};

const EditorSnippet = ({
  type = "html",
  placeholder = "Enter your HTML content here...",
  //   formData,
  //   setFormData,
  onValueChange,
  defaultValue = "",
  textareaId = "html",
}) => {
  const [code, setCode] = useState("");
  return (
    <>
      <Editor
        // value={formData.js_content}
        defaultValue={defaultValue}
        onValueChange={onValueChange}
        highlight={(code) => highlight(code, type)}
        padding={16}
        style={{
          fontFamily:
            'Monaco, Menlo, "Ubuntu Mono", Consolas, "Courier New", monospace',
          fontSize: 14,
          lineHeight: 1.6,
          backgroundColor: "#1e1e1e",
          color: "#d4d4d4",
          minHeight: "200px",
          outline: "none",
        }}
        placeholder={placeholder}
        textareaId={textareaId}
        className="tw-focus:tw-outline-none"
      />
    </>
  );
};

export default EditorSnippet;
