import React, { useState, useEffect } from "react";
import Sidebar from "../Layout/Sidebar";
import Header from "../Layout/Header";
import {
  BarChart3,
  Users,
  Globe,
  Component as Components,
  TrendingUp,
  Activity,
  FileText,
  Layout,
} from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { useAuth } from "../../contexts/AuthContext";

const Dashboard = () => {
  const { user } = useAuth();
  const api = useHttp();
  const [stats, setStats] = useState({
    websites: 0,
    components: 0,
    pages: 0,
    templates: 0,
  });
  const [recentActivity, setRecentActivity] = useState([]);

  useEffect(() => {
    // Fetch websites
    api.sendRequest(CONSTANTS.API.websites.get, (websites) => {
      setStats((prev) => ({ ...prev, websites: websites.length || 0 }));
    });

    // Fetch components
    api.sendRequest(CONSTANTS.API.components.get, (components) => {
      setStats((prev) => ({ ...prev, components: components.length || 0 }));
    });

    // Fetch pages
    api.sendRequest(CONSTANTS.API.pages.get, (pages) => {
      setStats((prev) => ({ ...prev, pages: pages.length || 0 }));
    });

    // Fetch templates
    api.sendRequest(CONSTANTS.API.templates.get, (templates) => {
      setStats((prev) => ({ ...prev, templates: templates.length || 0 }));
    });
  }, []);

  const statCards = [
    {
      name: "Total Websites",
      value: stats.websites,
      icon: Globe,
      color: "tw-from-blue-500 tw-to-blue-600",
      bgColor: "tw-bg-blue-50",
      iconColor: "tw-text-blue-600",
    },
    {
      name: "Components",
      value: stats.components,
      icon: Components,
      color: "tw-from-purple-500 tw-to-purple-600",
      bgColor: "tw-bg-purple-50",
      iconColor: "tw-text-purple-600",
    },
    {
      name: "Pages Created",
      value: stats.pages,
      icon: FileText,
      color: "tw-from-green-500 tw-to-green-600",
      bgColor: "tw-bg-green-50",
      iconColor: "tw-text-green-600",
    },
    {
      name: "Templates",
      value: stats.templates,
      icon: Layout,
      color: "tw-from-orange-500 tw-to-orange-600",
      bgColor: "tw-bg-orange-50",
      iconColor: "tw-text-orange-600",
    },
  ];

  const quickActions = [
    {
      name: "Create New Website",
      description: "Start building a new website from template",
      icon: Globe,
      color: "tw-from-blue-500 tw-to-blue-600",
      href: "/websites",
    },
    {
      name: "Build Component",
      description: "Create reusable components",
      icon: Components,
      color: "tw-from-purple-500 tw-to-purple-600",
      href: "/components",
    },
    {
      name: "Design Page",
      description: "Build pages with drag & drop",
      icon: FileText,
      color: "tw-from-green-500 tw-to-green-600",
      href: "/pages",
    },
    {
      name: "Manage Templates",
      description: "Organize page collections",
      icon: Layout,
      color: "tw-from-orange-500 tw-to-orange-600",
      href: "/templates",
    },
  ];

  if (api.isLoading) {
    return (
      <div className="tw-flex">
        <Sidebar />
        <div className="tw-flex-1 tw-ml-64">
          <Header
            title="Dashboard"
            subtitle="Welcome back to your Static Site Generator"
          />
          <div className="tw-p-6">
            <div className="tw-animate-pulse tw-space-y-6">
              <div className="tw-grid tw-grid-cols-1 tw-md:tw-grid-cols-2 tw-lg:tw-grid-cols-4 tw-gap-6">
                {[1, 2, 3, 4].map((i) => (
                  <div
                    key={i}
                    className="tw-bg-gray-300 tw-h-32 tw-rounded-xl"
                  ></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    // <div className="tw-flex">
    //   <Sidebar />
    //   <div className="tw-flex-1 tw-ml-64">
    //     <Header
    //       title={`Welcome back, ${user?.username}!`}
    //       subtitle="Manage your websites and components from your dashboard"
    //     />

    <div className="tw-p-6">
      {/* Stats Grid */}
      <div className="tw-grid tw-grid-cols-1 tw-md:tw-grid-cols-2 tw-lg:tw-grid-cols-4 tw-gap-6 tw-mb-8">
        {statCards.map((stat) => (
          <div
            key={stat.name}
            className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-p-6 tw-border tw-border-gray-100 tw-hover:tw-shadow-md tw-transition-shadow"
          >
            <div className="tw-flex tw-items-center tw-justify-between">
              <div>
                <p className="tw-text-sm tw-font-medium tw-text-gray-600">
                  {stat.name}
                </p>
                <p className="tw-text-3xl tw-font-bold tw-text-gray-900 tw-mt-2">
                  {stat.value}
                </p>
              </div>
              <div className={`tw-p-3 tw-rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`tw-w-6 tw-h-6 ${stat.iconColor}`} />
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="tw-grid tw-grid-cols-1 tw-lg:tw-grid-cols-2 tw-gap-8">
        {/* Quick Actions */}
        <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-p-6 tw-border tw-border-gray-100">
          <h2 className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-mb-4">
            Quick Actions
          </h2>
          <div className="tw-space-y-4">
            {quickActions.map((action) => (
              <a
                key={action.name}
                href={action.href}
                className="tw-flex tw-items-center tw-p-4 tw-rounded-lg tw-border tw-border-gray-200 tw-hover:tw-border-gray-300 tw-hover:tw-shadow-sm tw-transition-all tw-group"
              >
                <div
                  className={`tw-p-2 tw-rounded-lg tw-bg-gradient-to-r ${action.color} tw-text-white tw-group-hover:tw-scale-110 tw-transition-transform`}
                >
                  <action.icon className="tw-w-5 tw-h-5" />
                </div>
                <div className="tw-ml-4">
                  <h3 className="tw-font-medium tw-text-gray-900">
                    {action.name}
                  </h3>
                  <p className="tw-text-sm tw-text-gray-500">
                    {action.description}
                  </p>
                </div>
              </a>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-p-6 tw-border tw-border-gray-100">
          <h2 className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-mb-4">
            Getting Started
          </h2>
          <div className="tw-space-y-4">
            <div className="tw-flex tw-items-start tw-space-x-3">
              <div className="tw-bg-blue-100 tw-p-2 tw-rounded-lg">
                <span className="tw-text-blue-600 tw-font-bold tw-text-sm">
                  1
                </span>
              </div>
              <div>
                <h3 className="tw-font-medium tw-text-gray-900">
                  Create Categories
                </h3>
                <p className="tw-text-sm tw-text-gray-500">
                  Organize your components into categories
                </p>
              </div>
            </div>

            <div className="tw-flex tw-items-start tw-space-x-3">
              <div className="tw-bg-purple-100 tw-p-2 tw-rounded-lg">
                <span className="tw-text-purple-600 tw-font-bold tw-text-sm">
                  2
                </span>
              </div>
              <div>
                <h3 className="tw-font-medium tw-text-gray-900">
                  Build Components
                </h3>
                <p className="tw-text-sm tw-text-gray-500">
                  Create reusable HTML components with placeholders
                </p>
              </div>
            </div>

            <div className="tw-flex tw-items-start tw-space-x-3">
              <div className="tw-bg-green-100 tw-p-2 tw-rounded-lg">
                <span className="tw-text-green-600 tw-font-bold tw-text-sm">
                  3
                </span>
              </div>
              <div>
                <h3 className="tw-font-medium tw-text-gray-900">
                  Design Pages
                </h3>
                <p className="tw-text-sm tw-text-gray-500">
                  Drag and drop components to build pages
                </p>
              </div>
            </div>

            <div className="tw-flex tw-items-start tw-space-x-3">
              <div className="tw-bg-orange-100 tw-p-2 tw-rounded-lg">
                <span className="tw-text-orange-600 tw-font-bold tw-text-sm">
                  4
                </span>
              </div>
              <div>
                <h3 className="tw-font-medium tw-text-gray-900">
                  Create Templates
                </h3>
                <p className="tw-text-sm tw-text-gray-500">
                  Group pages into reusable templates
                </p>
              </div>
            </div>

            <div className="tw-flex tw-items-start tw-space-x-3">
              <div className="tw-bg-red-100 tw-p-2 tw-rounded-lg">
                <span className="tw-text-red-600 tw-font-bold tw-text-sm">
                  5
                </span>
              </div>
              <div>
                <h3 className="tw-font-medium tw-text-gray-900">
                  Generate Websites
                </h3>
                <p className="tw-text-sm tw-text-gray-500">
                  Create and export complete websites
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    //   </div>
    // </div>
  );
};

export default Dashboard;
