@tailwind base;
@tailwind components;
@tailwind utilities;

.component-tab-list:where(
    .css-dev-only-do-not-override-1vjf2v5
  ).ant-radio-button-wrapper {
  /* position: relative;
  display: inline-block;
  height: 32px;
  margin: 0;
  padding-inline: 15px;
  padding-block: 0;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  line-height: 30px;
  background: #ffffff; */
  border: 0px;
  border-block-start-width: 0px;
  border-inline-start-width: 0;
  border-inline-end-width: 0px;
  /* cursor: pointer;
  transition: color 0.2s, background 0.2s, box-shadow 0.2s; */
}

.component-tab-list {
  :where(
      .css-dev-only-do-not-override-1vjf2v5
    ).ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
    background: #2563eb !important;
    border-color: #2563eb !important;
    color: #fff;
  }

  :where(
      .css-dev-only-do-not-override-1vjf2v5
    ).ant-radio-button-wrapper-checked:not(
      .ant-radio-button-wrapper-disabled
    ):first-child {
    border-color: #2563eb;
  }
}
