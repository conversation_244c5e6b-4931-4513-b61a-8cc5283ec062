import React, { useState, useEffect } from "react";
import Sidebar from "../Layout/Sidebar";
import Header from "../Layout/Header";
import { Plus, Edit2, Trash2, FolderTree, Save, X } from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";

const CategoryManager = () => {
  const [categories, setCategories] = useState([]);
  // const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const api = useHttp();
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    color: "#3B82F6",
  });

  useEffect(() => {
    api.sendRequest(CONSTANTS.API.categories.get, (res) => {
      console.log(res, "res");
      // const data = res?.json();
      setCategories(res);
    });
    // fetchCategories();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();

    const apiConfig = editingCategory
      ? apiGenerator(CONSTANTS.API.categories.update, {
          id: editingCategory.id,
        })
      : // ? CONSTANTS.API.categories.update(editingCategory.id)
        CONSTANTS.API.categories.create;

    api.sendRequest(
      apiConfig,
      (res) => {
        console.log("Category saved successfully:", res);
        // Refresh categories list
        api.sendRequest(CONSTANTS.API.categories.get, (res) => {
          setCategories(res);
        });
        resetForm();
      },
      formData,
      `Category ${editingCategory ? "updated" : "created"} successfully!`
    );
  };

  const handleEdit = (category) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      description: category.description || "",
      color: category.color || "#3B82F6",
    });
    setShowForm(true);
  };

  const handleDelete = async (id) => {
    if (confirm("Are you sure you want to delete this category?")) {
      api.sendRequest(
        apiGenerator(CONSTANTS.API.categories.delete, { id }),
        (res) => {
          console.log("Category deleted successfully:", res);
          // Refresh categories list
          api.sendRequest(CONSTANTS.API.categories.get, (res) => {
            setCategories(res);
          });
        },
        null,
        "Category deleted successfully!"
      );
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      color: "#3B82F6",
    });
    setEditingCategory(null);
    setShowForm(false);
  };

  const predefinedColors = [
    "#3B82F6",
    "#8B5CF6",
    "#10B981",
    "#F59E0B",
    "#EF4444",
    "#06B6D4",
    "#84CC16",
    "#F97316",
  ];

  if (api.isLoading) {
    return (
      <div className="tw-flex">
        <Sidebar />
        <div className="tw-flex-1 tw-ml-64">
          <div className="tw-animate-pulse tw-p-6">
            <div className="tw-h-8 tw-bg-gray-300 tw-rounded tw-mb-4"></div>
            <div className="tw-space-y-4">
              {[1, 2, 3].map((i) => (
                <div
                  key={i}
                  className="tw-h-20 tw-bg-gray-300 tw-rounded"
                ></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tw-flex">
      <Sidebar />
      <div className="tw-flex-1 tw-ml-64">
        <Header
          title="Category Management"
          subtitle="Organize your components into categories for better management"
        />

        <div className="tw-p-6">
          <div className="tw-flex tw-justify-between tw-items-center tw-mb-6">
            <div className="tw-flex tw-items-center">
              <FolderTree className="tw-w-6 tw-h-6 tw-text-blue-600 tw-mr-2" />
              <h2 className="tw-text-xl tw-font-semibold tw-text-gray-900">
                Categories ({categories.length})
              </h2>
            </div>

            <button
              onClick={() => setShowForm(true)}
              className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-4 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all tw-flex tw-items-center"
            >
              <Plus className="tw-w-4 tw-h-4 tw-mr-2" />
              Add Category
            </button>
          </div>

          {/* Category Form Modal */}
          {showForm && (
            <div className="tw-fixed tw-inset-0 tw-bg-black tw-bg-opacity-50 tw-flex tw-items-center tw-justify-center tw-z-50">
              <div className="tw-bg-white tw-rounded-xl tw-p-6 tw-w-full tw-max-w-md tw-m-4">
                <div className="tw-flex tw-justify-between tw-items-center tw-mb-4">
                  <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900">
                    {editingCategory ? "Edit Category" : "Add New Category"}
                  </h3>
                  <button
                    onClick={resetForm}
                    className="tw-text-gray-400 tw-hover:tw-text-gray-600"
                  >
                    <X className="tw-w-5 tw-h-5" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="tw-space-y-4">
                  <div>
                    <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                      Category Name
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) =>
                        setFormData({ ...formData, name: e.target.value })
                      }
                      className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                      placeholder="e.g., Headers, Content, Forms"
                      required
                    />
                  </div>

                  <div>
                    <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                      Description
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          description: e.target.value,
                        })
                      }
                      className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-h-20"
                      placeholder="Brief description of this category"
                      rows="3"
                    />
                  </div>

                  <div>
                    <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                      Color
                    </label>
                    <div className="tw-flex tw-space-x-2 tw-mb-2">
                      {predefinedColors.map((color) => (
                        <button
                          key={color}
                          type="button"
                          onClick={() => setFormData({ ...formData, color })}
                          className={`tw-w-8 tw-h-8 tw-rounded-full tw-border-2 tw-transition-all ${
                            formData.color === color
                              ? "tw-border-gray-900 tw-scale-110"
                              : "tw-border-gray-300 tw-hover:tw-scale-105"
                          }`}
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                    <input
                      type="color"
                      value={formData.color}
                      onChange={(e) =>
                        setFormData({ ...formData, color: e.target.value })
                      }
                      className="tw-w-full tw-h-10 tw-rounded-lg tw-border tw-border-gray-300"
                    />
                  </div>

                  <div className="tw-flex tw-space-x-3 tw-pt-4">
                    <button
                      type="submit"
                      className="tw-flex-1 tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-py-2 tw-px-4 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all tw-flex tw-items-center tw-justify-center"
                    >
                      <Save className="tw-w-4 tw-h-4 tw-mr-2" />
                      {editingCategory ? "Update" : "Create"}
                    </button>
                    <button
                      type="button"
                      onClick={resetForm}
                      className="tw-flex-1 tw-bg-gray-300 tw-text-gray-700 tw-py-2 tw-px-4 tw-rounded-lg tw-font-medium tw-hover:tw-bg-gray-400 tw-transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}

          {/* Categories Grid */}
          <div className="tw-grid tw-grid-cols-1 tw-md:tw-grid-cols-2 tw-lg:tw-grid-cols-3 tw-gap-6">
            {categories.map((category) => (
              <div
                key={category.id}
                className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-p-6 tw-hover:tw-shadow-md tw-transition-shadow"
              >
                <div className="tw-flex tw-items-center tw-justify-between tw-mb-4">
                  <div className="tw-flex tw-items-center">
                    <div
                      className="tw-w-4 tw-h-4 tw-rounded-full tw-mr-3"
                      style={{ backgroundColor: category.color }}
                    />
                    <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900">
                      {category.name}
                    </h3>
                  </div>

                  <div className="tw-flex tw-space-x-2">
                    <button
                      onClick={() => handleEdit(category)}
                      className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-blue-600 tw-hover:tw-bg-blue-50 tw-rounded-lg tw-transition-colors"
                    >
                      <Edit2 className="tw-w-4 tw-h-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(category.id)}
                      className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-red-600 tw-hover:tw-bg-red-50 tw-rounded-lg tw-transition-colors"
                    >
                      <Trash2 className="tw-w-4 tw-h-4" />
                    </button>
                  </div>
                </div>

                <p className="tw-text-gray-600 tw-text-sm tw-mb-4">
                  {category.description || "No description provided"}
                </p>

                <div className="tw-flex tw-justify-between tw-items-center tw-text-xs tw-text-gray-500">
                  <span>ID: {category.id}</span>
                  <span>
                    {new Date(category.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {categories.length === 0 && (
            <div className="tw-text-center tw-py-12">
              <FolderTree className="tw-w-16 tw-h-16 tw-text-gray-300 tw-mx-auto tw-mb-4" />
              <h3 className="tw-text-lg tw-font-medium tw-text-gray-900 tw-mb-2">
                No categories yet
              </h3>
              <p className="tw-text-gray-500 tw-mb-4">
                Create your first category to organize components
              </p>
              <button
                onClick={() => setShowForm(true)}
                className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-6 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all"
              >
                Create Category
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CategoryManager;
